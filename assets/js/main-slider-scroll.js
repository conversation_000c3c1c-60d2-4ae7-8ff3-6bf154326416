document.addEventListener('DOMContentLoaded', function () {
    const slider = document.querySelector('.home_banner .vertical-parallax-slider');
    if (!slider) return;

    let scrollUnlocked = false;
    let scrollCooldown = false;

    window.scrollUnlocked = scrollUnlocked;

    // Automatyczne przewijanie slidera
    let autoScrollTimer = null;
    let autoScrollEnabled = true;
    let autoScrollInterval = 3000;
    let userInteracted = false;
    let isAutoScrolling = false;

    function goToNextSlide() {
        const slides = slider.querySelectorAll('.slide.item');
        const activeSlide = slider.querySelector('.slide.item.active');
        if (!activeSlide) return;

        const activeIndex = Array.from(slides).indexOf(activeSlide);
        const lastIndex = slides.length - 1;

        // Jeśli jesteśmy na ostatnim slajdzie, zatrzymaj automatyczne przewijanie
        if (activeIndex >= lastIndex) {
            stopAutoScroll();
            return;
        }

        // Oznacz że trwa automatyczne przewijanie
        isAutoScrolling = true;

        const homeBanner = document.querySelector('.home_banner');
        const nextArrow = homeBanner ? homeBanner.querySelector('.nav-arrows .prev') : null;

        if (nextArrow) {
            nextArrow.click();
        } else {
            const wheelEvent = new WheelEvent('wheel', {
                deltaY: 100,
                bubbles: true,
                cancelable: true
            });
            slider.dispatchEvent(wheelEvent);
        }

        setTimeout(() => {
            isAutoScrolling = false;
        }, 100);

        setTimeout(() => {
            updateScrollLock();
        }, 200);
    }

    // Funkcja do uruchomienia automatycznego przewijania
    function startAutoScroll() {
        if (!autoScrollEnabled || userInteracted) return;

        stopAutoScroll();

        autoScrollTimer = setInterval(() => {
            if (!autoScrollEnabled || userInteracted || scrollUnlocked) {
                stopAutoScroll();
                return;
            }
            goToNextSlide();
        }, autoScrollInterval);
    }

    // Funkcja do zatrzymania automatycznego przewijania
    function stopAutoScroll() {
        if (autoScrollTimer) {
            clearInterval(autoScrollTimer);
            autoScrollTimer = null;
        }
    }

    // Funkcja do oznaczenia interakcji użytkownika
    function markUserInteraction() {
        userInteracted = true;
        stopAutoScroll();
    }

    function updateScrollLock() {
        const slides = slider.querySelectorAll('.slide.item');
        const activeSlide = slider.querySelector('.slide.item.active');
        if (!activeSlide) return;

        const activeIndex = Array.from(slides).indexOf(activeSlide);
        const lastIndex = slides.length - 1;

        const skarpa = document.querySelector('.skarpa_text');
        if (skarpa) {
            skarpa.style.transition = 'margin-top 0.5s ease';
            skarpa.style.marginTop = (activeIndex === lastIndex) ? '-220px' : '0px';
        }

        const homeBanner = document.querySelector('.home_banner');

        if (activeIndex === lastIndex) {
            if (!scrollUnlocked) {
                if (!scrollCooldown) {
                    scrollCooldown = true;
                    setTimeout(() => {
                        scrollCooldown = false;
                        scrollUnlocked = true; // dopiero po 1s odblokuj przewijanie strony
                        window.scrollUnlocked = true;

                        document.body.classList.remove('scroll-locked');
                        document.body.style.overflow = 'auto';

                        if (homeBanner) {
                            homeBanner.style.position = 'relative';
                            homeBanner.style.overflow = 'visible';
                            homeBanner.style.pointerEvents = 'none';
                        }

                        const wheelReenable = (e) => {
                            const homeBanner = document.querySelector('.home_banner');
                            if (!homeBanner) return;

                            const rect = homeBanner.getBoundingClientRect();
                            const fullyVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;

                            if (!fullyVisible) return;

                            if (e.deltaY < 0) {
                                homeBanner.style.pointerEvents = 'auto';
                                document.body.classList.add('scroll-locked');
                                document.body.style.overflow = 'hidden';
                                scrollUnlocked = false;
                                window.scrollUnlocked = false;

                                const prevArrow = homeBanner.querySelector('.nav-arrows .next');
                                if (prevArrow) {
                                    prevArrow.click();
                                    setTimeout(() => {
                                        updateScrollLock();
                                    }, 200);
                                } else {
                                    const slides = slider.querySelectorAll('.slide.item');
                                    const activeSlide = slider.querySelector('.slide.item.active');
                                    const activeIndex = Array.from(slides).indexOf(activeSlide);

                                    if (activeIndex > 0) {
                                        activeSlide.classList.remove('active');
                                        slides[activeIndex - 1].classList.add('active');
                                        setTimeout(() => {
                                            updateScrollLock();
                                        }, 200);
                                    }
                                }

                                window.removeEventListener('wheel', wheelReenable, true);
                            }
                        };
                        window.addEventListener('wheel', wheelReenable, true);
                    }, 700);
                }
            }
        }
    }

    function wheelListener(e) {
        if (!isAutoScrolling) {
            markUserInteraction();
        }

        if (scrollCooldown) return;
        const deltaY = e.deltaY;
        const isScrollingDown = deltaY > 8;

        const slides = slider.querySelectorAll('.slide.item');
        const activeSlide = slider.querySelector('.slide.item.active');
        if (!activeSlide) return;

        const activeIndex = Array.from(slides).indexOf(activeSlide);
        const lastIndex = slides.length - 1;

        if (!scrollUnlocked) {
            e.preventDefault();
        }

        if (scrollCooldown) return;

        if (isScrollingDown && activeIndex === lastIndex && !scrollUnlocked) {
            updateScrollLock();
            return;
        }

        scrollCooldown = true;
        setTimeout(() => {
            scrollCooldown = false;
        }, 900);
    }

    // --- Touch obsługa ---
    let touchStartY = null;
    let touchEndY = null;

    function handleTouchStart(e) {
        markUserInteraction();
        touchStartY = e.touches[0].clientY;
    }

    function handleTouchMove(e) {
        touchEndY = e.touches[0].clientY;
    }

    function handleTouchEnd(e) {
        if (!touchStartY || !touchEndY) return;
        if (scrollCooldown) return;

        const deltaY = touchStartY - touchEndY;
        const isScrollingDown = deltaY > 30;
        const isScrollingUp = deltaY < -30;

        const slides = slider.querySelectorAll('.slide.item');
        const activeSlide = slider.querySelector('.slide.item.active');
        if (!activeSlide) return;

        const activeIndex = Array.from(slides).indexOf(activeSlide);
        const lastIndex = slides.length - 1;

        if (isScrollingDown && activeIndex === lastIndex && !scrollUnlocked) {
            if (!scrollCooldown) {
                scrollCooldown = true;
                setTimeout(() => {
                    scrollCooldown = false;
                    scrollUnlocked = true;
                    window.scrollUnlocked = true;
                    document.body.classList.remove('scroll-locked');
                    document.body.style.overflow = 'auto';
                }, 700);
            }
            return;
        }

        if (!scrollUnlocked && (isScrollingDown || isScrollingUp)) {
            e.preventDefault();
        }

        scrollCooldown = true;
        setTimeout(() => {
            scrollCooldown = false;
        }, 900);

        touchStartY = null;
        touchEndY = null;
    }

    slider.addEventListener('wheel', wheelListener, { passive: false });
    slider.addEventListener('touchstart', handleTouchStart, { passive: true });
    slider.addEventListener('touchmove', handleTouchMove, { passive: true });
    slider.addEventListener('touchend', handleTouchEnd, { passive: false });

    document.addEventListener('keydown', (e) => {
        // Oznacz interakcję użytkownika przy użyciu klawiatury
        if (['ArrowDown', 'ArrowUp', 'PageDown', 'PageUp', ' '].includes(e.key)) {
            markUserInteraction();
        }

        if (!scrollUnlocked && ['ArrowDown', 'PageDown', ' '].includes(e.key)) {
            e.preventDefault();
        }
        updateScrollLock();
    });

    setInterval(updateScrollLock, 400);

    // Uruchom automatyczne przewijanie po załadowaniu strony
    setTimeout(() => {
        if (!userInteracted) {
            startAutoScroll();
        }
    }, 1000);

});

document.addEventListener("DOMContentLoaded", () => {
    const target = document.querySelector(".vertical-parallax-slider");

    if (target) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    document.body.classList.add('scroll-locked');
                    document.body.style.overflow = 'hidden';
                    scrollUnlocked = false;
                    window.scrollUnlocked = false;
                    observer.unobserve(target);
                }
            });
        }, { threshold: 0 });

        observer.observe(target);
    }
});
