# Menu Navigation - Dokumentacja (Zaktualizowana)

## Opis funkcjonalności

Dodano funkcjonalność automatycznego podkreślania aktywnych elementów menu w dwóch wariantach:

1. **Kotwice HTML** - dla sekcji na stronie głównej (automatycznie wykrywane z menu)
2. **Podstrony** - dla odnośników do podstron (np. /oferta)

## Kluczowe poprawki

### ✅ **1. Dynamiczne wykrywanie sekcji**
- ID sekcji są teraz **automatycznie zaczytywane z menu** na stronie
- Nie ma już sztywno zakodowanej tablicy ID w kodzie
- Gdy ktoś zmieni zakładki w menu, funkcjonalność nadal będzie działać

### ✅ **2. Tylko jedna aktywna zakładka**
- Algorytm zapewnia, że **tylko jedna zakładka** jest podświ<PERSON>lona jednocześnie
- Gdy dwie sekcje są wido<PERSON>ne w viewport, wyświ<PERSON><PERSON> jest **pierwsza** (wyższa na stronie)
- Sekcje są automatycznie sortowane według pozycji na stronie

## Jak to działa

### Dla kotwic HTML (sekcje na stronie)
1. **Dynamiczne wykrywanie sekcji** - skrypt automatycznie skanuje menu i znajduje wszystkie linki zaczynające się od `#`
2. **Automatyczne mapowanie** - dla każdego znalezionego linka sprawdza, czy istnieje odpowiadająca mu sekcja na stronie
3. **Sortowanie według pozycji** - sekcje są sortowane według ich pozycji na stronie (od góry do dołu)
4. **Wykrywanie jednej aktywnej sekcji** - podczas przewijania sprawdza sekcje w kolejności i aktywuje **tylko pierwszą** spełniającą warunki
5. Dodaje klasę `menu-active` do odpowiedniego elementu menu
6. CSS wyświetla podkreślenie pod aktywnym elementem

### Dla podstron
1. Skrypt sprawdza aktualną ścieżkę URL (`window.location.pathname`)
2. Jeśli ścieżka zawiera `/oferta`, aktywuje odpowiedni element menu
3. Podkreślenie pozostaje aktywne podczas przebywania na podstronie

## Algorytm wykrywania aktywnej sekcji

```javascript
// Przejdź przez sekcje w kolejności od góry do dołu
for (let i = 0; i < this.sections.length; i++) {
    const section = this.sections[i];
    const sectionTop = window.scrollY + rect.top;
    const sectionBottom = sectionTop + rect.height;
    
    // Sekcja jest aktywna jeśli punkt odniesienia jest między jej górą a dołem
    if (sectionTop <= scrollPosition && sectionBottom > scrollPosition) {
        activeSection = section;
        break; // WAŻNE: Przerwij pętlę - tylko pierwsza pasująca sekcja
    }
}
```

## Pliki zmodyfikowane

### 1. `assets/css/style.css` - Style CSS (bez zmian)
### 2. `assets/js/menu-navigation.js` - Główne poprawki:

**Metoda `collectSections()` - NOWA:**
```javascript
collectSections() {
    this.sections = [];
    
    // Zbierz wszystkie linki z menu, które wskazują na kotwice
    const anchorLinks = this.menuItems
        .map(item => item.querySelector('a'))
        .filter(link => link && link.getAttribute('href')?.startsWith('#'));
    
    anchorLinks.forEach(link => {
        const href = link.getAttribute('href');
        const id = href.substring(1);
        const element = document.getElementById(id);
        const menuItem = link.closest('.e-n-menu-title');
        
        if (element && menuItem) {
            this.sections.push({ id, element, menuItem, link, href });
        }
    });
    
    // Sortuj sekcje według pozycji na stronie
    this.sections.sort((a, b) => {
        const aTop = a.element.getBoundingClientRect().top + window.scrollY;
        const bTop = b.element.getBoundingClientRect().top + window.scrollY;
        return aTop - bTop;
    });
}
```

**Metoda `checkActiveSection()` - POPRAWIONA:**
- Używa pętli `for` z `break` zamiast `forEach`
- Zapewnia aktywację tylko pierwszej pasującej sekcji
- Lepszy algorytm wykrywania na podstawie pozycji scroll

### 3. `includes/enqueue.php` - Ładowanie plików (bez zmian)

## Debugowanie

Dodano metodę debugowania:

```javascript
// Sprawdź znalezione sekcje i aktywny element
window.menuNavigation.debug();

// Ręcznie odśwież stan menu
window.menuNavigation.refresh();
```

## Zalety nowego rozwiązania

1. **Elastyczność** - automatycznie dostosowuje się do zmian w menu
2. **Niezawodność** - tylko jedna zakładka aktywna jednocześnie
3. **Wydajność** - optymalizowany algorytm z przerwaniem pętli
4. **Łatwość utrzymania** - brak sztywno zakodowanych ID
5. **Debugowanie** - wbudowane narzędzia diagnostyczne

## Kompatybilność

- **WordPress** - kompatybilne z WordPress
- **Elementor** - działa z menu Elementor
- **Dynamiczne menu** - automatycznie dostosowuje się do zmian
- **Przeglądarki** - wszystkie nowoczesne przeglądarki
- **Urządzenia mobilne** - w pełni responsywne
