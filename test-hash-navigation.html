<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hash Navigation</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
        }
        
        .home_banner {
            height: 100vh;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .vertical-parallax-slider {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        .slide.item {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2em;
        }
        
        .nav-arrows .prev {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: #333;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
        }
        
        .menu {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .e-n-menu-title {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            transition: background-color 0.3s;
        }
        
        .e-n-menu-title.menu-active {
            background-color: #007cba;
            color: white;
        }
        
        .e-n-menu-title a {
            text-decoration: none;
            color: inherit;
        }
        
        .section {
            height: 100vh;
            padding: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
        }
        
        #domki { background: #ffeb3b; }
        #restauracja { background: #4caf50; }
        #spa { background: #2196f3; }
        #kontakt { background: #ff5722; }
        
        .scroll-locked {
            overflow: hidden !important;
        }
        
        .test-links {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-links a {
            display: block;
            margin: 5px 0;
            padding: 5px 10px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
    </style>
</head>
<body class="scroll-locked">
    <div class="menu">
        <h3>Menu</h3>
        <div class="e-n-menu-title">
            <a href="#domki">Domki</a>
        </div>
        <div class="e-n-menu-title">
            <a href="#restauracja">Restauracja</a>
        </div>
        <div class="e-n-menu-title">
            <a href="#spa">SPA</a>
        </div>
        <div class="e-n-menu-title">
            <a href="#kontakt">Kontakt</a>
        </div>
    </div>
    
    <div class="test-links">
        <h3>Test Links</h3>
        <a href="#domki">Link do Domków</a>
        <a href="#restauracja">Link do Restauracji</a>
        <a href="#spa">Link do SPA</a>
        <a href="#kontakt">Link do Kontaktu</a>
        <button onclick="lockScroll()">Zablokuj scroll</button>
    </div>

    <div class="home_banner">
        <div class="vertical-parallax-slider">
            <div class="slide item">Slide 1</div>
            <div class="slide item">Slide 2</div>
            <div class="slide item">Slide 3</div>
            <div class="nav-arrows">
                <button class="prev">Next Slide</button>
            </div>
        </div>
    </div>

    <div id="domki" class="section">
        <h2>Sekcja Domki</h2>
    </div>

    <div id="restauracja" class="section">
        <h2>Sekcja Restauracja</h2>
    </div>

    <div id="spa" class="section">
        <h2>Sekcja SPA</h2>
    </div>

    <div id="kontakt" class="section">
        <h2>Sekcja Kontakt</h2>
    </div>

    <script src="assets/js/menu-navigation.js"></script>
    <script>
        function lockScroll() {
            document.body.classList.add('scroll-locked');
            document.body.style.overflow = 'hidden';
        }
        
        // Symulacja kliknięcia w slider
        let slideIndex = 0;
        document.querySelector('.nav-arrows .prev').addEventListener('click', function() {
            slideIndex++;
            console.log('Slider moved to slide:', slideIndex);
        });
        
        // Test hash navigation
        console.log('Test hash navigation loaded');
        console.log('Current hash:', window.location.hash);
        
        // Dodaj event listener do testowania
        window.addEventListener('hashchange', function() {
            console.log('Hash changed to:', window.location.hash);
        });
    </script>
</body>
</html>
