.portfolio-navigation .num.active span {
    color: #c59156 !important;
    font-size: 16px;
}
.splitscroll-wrapper .item .cell {
    height: auto !important;
    margin-top: 64px !important;
}
.split-screen.yes {
    overflow: hidden;
    max-height: 100vh;
}


html, body {
    overflow-y: auto !important;
    height: auto !important;
}

.scroll-locked {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

.fix-scroll {
    overflow: visible !important;
    height: auto !important;
    position: relative !important;
}
/* Full height scrollable slider container */
.splitsc {
    overflow: hidden;
    position: relative;
}

/* Individual full-screen slides */
.splitsc .splitslide {
    height: 100vh;
    width: 100%;
    flex-shrink: 0;
    scroll-snap-align: start;
}

/* Container styling for vertical scroll */
.splitscroll-wrapper {
    height: 100vh;
    overflow-y: scroll;
    scroll-snap-type: y mandatory;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    display: flex;
    flex-direction: column;
}

/* Główny slider na stronie <PERSON>ł<PERSON>j (hero) */

.vertical-banner .cell {
    max-width: 100% !important;
    display: flex;
    flex-direction: column;
    align-items: end;
}

.vertical-parallax-slider .item .cell {
    max-width: 100% !important;
}

.vertical-parallax-slider .item.active .cell {
    transform: translateY(5vh) !important;
    -webkit-transform: translateY(5vh) !important;
}

.category-slider-area .category-slider-images .img-item .num {
    font-size: 2.5em !important;
    opacity: 1 !important;
    left: 90px !important;
    bottom: 50px !important;
    color: #c38f54;
}

.vertical-banner .cell .text {
    text-align: center;
    width: fit-content;
    font-size: 12px !important;
}

.slajder {
    height: auto !important;
    overflow: hidden;
}

@media (min-width: 1441px) {
    .slajder {
        max-height: 90vh !important;
    }
}

@media (max-width: 1440px) {
    .slajder {
        width: 100%;
        max-height: 600px !important;
    }

    .category-slider-area .category-slider-images .img-item {
        top: 30px !important;
        bottom: 30px !important;
        right: 30px !important;
        left: 30px !important;
    }

    .slajder .img-item {
        background-size: cover !important;
        background-position: center center !important;
        max-height: 600px !important;
    }
    .category-slider-area .category-slider-images .img-item .num {
        left: 50px !important;
        bottom: 50px !important;
    }
}

@media (max-width: 1024px) {
    .slajder {
        width: 100%;
        max-height: 390px !important;
    }

    .slajder .img-item {
        background-size: cover !important;
        background-position: center center !important;
        max-height: 390px !important;
    }
}

@media (max-width: 767px) {
    .slajder {
        width: 100%;
        max-height: 250px !important;
    }

    .slajder .img-item {
        max-height: 250px !important;
    }

    .category-slider-area .category-slider-images .img-item {
        top: 0 !important;
        bottom: 0 !important;
    }

    .category-slider-area .category-slider-images .img-item .num {
        left: 30px !important;
        bottom: 10px !important;
        font-size: 26px !important;
    }
}

.skarpa-main-slider-image .e-con-inner {
    margin: 0 !important;
}

.hero-slider-first-line {
    margin-right: 220px;
}

.hero-slider-second-line {
    margin-right: 80px;
}

.vertical-parallax-slider .item.prev .cell,
.vertical-parallax-slider .item.active .cell {
    height: auto !important;
}

.vertical-parallax-slider .slide.item > .container {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
}

.vertical-parallax-slider .cell {
    max-width: 320px !important;
}

.vertical-parallax-slider .cell .h {
    font-size: 36px;
    white-space: normal;
    word-wrap: break-word;
    font-weight: 400;
    margin-bottom: 7px !important;
}

[data-id="095ab3f"] {
    margin-bottom: 4px !important;
}

.banner-social-buttons {
    color: white;
}

.owl-carousel .owl-item {
    opacity: 0.7 !important;
}
.owl-carousel .owl-item a {
    color: white;
}

.owl-carousel .owl-item.active.center {
    opacity: 1 !important;
}
.owl-carousel .owl-item.active.center a {
    color: #c38f54;
}
.elementor-kit-7 a {
    font-family: "futura medium", Sans-serif !important;
}


@media (min-width: 2300px) {
    .category-slider-area .category-slider-images .img-item .num {
        bottom: 70px !important;
    }
}

body.scroll-locked {
    overflow: hidden;
    touch-action: none;
}
@media (max-width: 1200px) {
    .nav-arrows > div {
        font-size: 32px;
    }
    .nav-arrows {
        background: rgba(255, 255, 255, 0.7) !important;
        border-radius: 8px;
        padding: 12px !important;
    }
    .nav-arrows > .prev {
        margin-right: 0px !important;
    }
}

.column_text {
    margin-top: 20px;
}

.split-screen {
    overflow: visible;
}

.split-screen .cell div {
    padding-right: 0 !important;
}

.split-screen>.item>.content>.cell {
    height: 100% !important;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 !important;
    max-width: 100%;
}

.split-screen>.item>.content {
    height: 100% !important;
}

.split-screen>.item .img-item {
    height: 760px !important;
}

@media (max-width: 1200px) {
    .split-screen>.item .img-item {
        height: 720px !important;
    }
}

.split-screen>.item .image {
    position: static;
}

.column_text .wrap_text {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: fit-content !important;
}

.split-screen>.item {
    display: flex;
    align-items: center;
}

@media (min-width: 1441px) {
    .custom-wide-box {
        width: 80vw;       /* szerokość względem viewportu */
        max-width: none;   /* brak ograniczenia szerokości kontenerem */

    }
}
html, body {
    overflow-x: hidden;
}

.category-slider-area .category-slider a {
    font-size: 55px !important;
}

/* Rozciągnięcie obrazu w lightboxie do pełnego ekranu */
.elementor-lightbox .elementor-lightbox-item img {
    width: 100vw;
    height: 100vh;
    object-fit: contain; /* lub 'cover' jeśli chcesz przycięcie */
    margin: 0;
    padding: 0;
    display: block;
}

/* Usunięcie marginesów i zaokrągleń */
.elementor-lightbox .elementor-lightbox-item {
    padding: 0 !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}

/* Nawigacja nad obrazem */
.elementor-lightbox .elementor-lightbox-navigation {
    z-index: 1001;
}

/* Tło lightboxa bez szarej ramki */
.elementor-lightbox .elementor-lightbox-container {
    background: rgba(0, 0, 0, 0.95) !important;
}

/* ===== MENU NAVIGATION STYLES ===== */
/* Podstawowe style dla elementów menu */
.e-n-menu-title-container {
    position: relative;
    transition: all 0.3s ease;
}

/* Podkreślenie dla aktywnego elementu menu */
.e-n-menu-title-container::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    width: 0;
    height: 8px;
    background-color: #BA834B;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

/* Aktywny element menu - podkreślenie */
.e-n-menu-title.menu-active .e-n-menu-title-container::after,
.e-n-menu-title.e-current .e-n-menu-title-container::after {
    width: calc(100% + 15px);
}

/* Responsywność dla menu mobilnego */
@media (max-width: 1024px) {
    .e-n-menu-title-container::after {
        bottom: -3px;
        height: 2px;
    }
}

@media (max-width: 767px) {
    .e-n-menu-title-container::after {
        bottom: -2px;
        height: 1px;
    }
}
