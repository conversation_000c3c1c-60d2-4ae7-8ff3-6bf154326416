<?php

const DD_PLUGIN_SKARPA_VERSION = '1.0.0';

function dd_load_stylesheets() {
    wp_register_style('style', DD_PLUGIN_DIR_URL . '/assets/css/style.css', array(), DD_PLUGIN_SKARPA_VERSION);
    wp_enqueue_style('style');
}

add_action('wp_enqueue_scripts', 'dd_load_stylesheets');

function dd_load_js() {
    wp_register_script('main-slider', DD_PLUGIN_DIR_URL . '/assets/js/main-slider-scroll.js', array(), DD_PLUGIN_SKARPA_VERSION, 1);
    wp_enqueue_script('main-slider');
    wp_register_script('menu-navigation', DD_PLUGIN_DIR_URL . '/assets/js/menu-navigation.js', array(), DD_PLUGIN_SKARPA_VERSION, 1);
    wp_enqueue_script('menu-navigation');
    wp_register_script('script', DD_PLUGIN_DIR_URL . '/assets/js/main.js', array(), DD_PLUGIN_SKARPA_VERSION, 1);
    wp_enqueue_script('script');
}

add_action('wp_enqueue_scripts', 'dd_load_js');